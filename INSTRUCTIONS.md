# 🏠 Family Chore Assignment System - Development Instructions

## Core Concept
Build a single-file HTML application that randomly assigns household chores to family members with intelligent balancing to ensure fairness over time. The system should be completely offline, use localStorage for data persistence, and work on any device with a web browser.

## Key Design Principles
- **Simplicity First**: Remove unnecessary complexity (no age groups, categories, etc.)
- **Exempt-Only Assignment**: Everyone can do every job unless explicitly exempt
- **Smart Dependencies**: Support jobs that require other jobs to be done first
- **Configurable Algorithm**: User can tune the balancing algorithm
- **Mobile-Friendly**: Works perfectly on phones and tablets
- **Print-Ready**: Clean printable weekly schedules

## Data Structures

### Family Member
```javascript
{
    id: timestamp,           // Unique identifier
    name: "string",         // Person's name
    addedDate: "ISO date"   // When added to system
}
```

### Job
```javascript
{
    id: timestamp,              // Unique identifier
    name: "string",            // Job name (e.g., "Clean Bathroom")
    points: number,            // Difficulty/time score (1-10)
    frequency: "string",       // "Daily", "Every 2 days", "Every 3 days", "Weekly", "Monthly"
    exemptPeople: [ids],       // Array of family member IDs who should NEVER do this job
    requiresPreJob: boolean,   // Whether this job needs another job done first
    preJobId: number|null,     // ID of required job (if any)
    preJobTiming: "string",    // "same-day", "next-day", "flexible"
    addedDate: "ISO date"      // When job was created
}
```

### Settings
```javascript
{
    workloadBalance: 0.5,      // How strongly to enforce equal point distribution (0.1-1.0)
    recentPenalty: 0.6,        // Penalty for doing same job recently (0.1-2.0)
    randomness: 0.3,           // Amount of randomness vs deterministic (0.0-1.0)
    historyWeeks: 3,           // How many weeks back to consider (1-5)
    splitThreshold: 8          // When to split pre-job + dependent job (3-15 points)
}
```

### History Entry
```javascript
{
    id: timestamp,
    date: "ISO date",
    weekStarting: "readable date",
    roster: {
        "PersonName": {
            "Monday": [job objects],
            "Tuesday": [job objects],
            // ... all 7 days
        }
    }
}
```

## User Interface Structure

### Navigation Tabs
1. **🎲 Generate Roster** (default active)
2. **👨‍👩‍👧‍👦 Manage Family**
3. **🧹 Manage Jobs**
4. **⚙️ Settings**
5. **📋 View History**

## Tab Functionality

### 1. Generate Roster Tab
- **Large button**: "🎲 Generate Weekly Roster"
- **Results area**: Shows generated assignments
- **Balance summary**: Point totals and fairness metrics
- **Print button**: "🖨️ Print Weekly Roster"

**Display Format:**
- Grid of person cards (responsive)
- Each card shows person name and total points
- Days organized as columns within each person card
- Jobs color-coded by difficulty (easy=green, medium=yellow, hard=red)

### 2. Manage Family Tab
**Add Family Member:**
- Name input field
- "Add Family Member" button

**Family List:**
- Shows all family members
- Each has a "Remove" button
- Empty state message when none added

### 3. Manage Jobs Tab
**Add Job Section:**
- **Job Name** (text input)
- **Scoring Method** (radio buttons):
  - 🎯 Set my own score (manual 1-10 slider)
  - 🧙‍♂️ Use scoring wizard (3 questions)
- **Frequency** (dropdown): Daily, Every 2 days, Every 3 days, Weekly, Monthly
- **Assignment Rules** (checkbox section):
  - ☐ "Some people should never do this job"
  - When checked: Show checkboxes for all family members
  - Label: "Who should NEVER do this job:"
- **Dependencies** (checkbox section):
  - ☐ "Requires another job to be done first"
  - When checked: Dropdown of existing jobs + timing option
- **"Add Job" button**

**Scoring Wizard Questions:**
1. How long does this job take? (Quick <5min, Medium 5-20min, Long 20+min, Very long 1+hour)
2. Physical effort required? (Easy/sitting, Light, Moderate, Heavy)
3. How unpleasant? (Enjoyable, Neutral, Somewhat unpleasant, Really gross)
- Auto-calculate points based on answers
- Show calculation breakdown

**Jobs List:**
- Shows all jobs with point badges
- Subtitle shows frequency, exemptions, dependencies
- Each has "Remove" button

### 4. Settings Tab
**Balancing Strength:**
- Workload Balance Strength (slider 0.1-1.0)
- Recent Assignment Penalty (slider 0.1-2.0)
- Randomness Factor (slider 0.0-1.0)

**Assignment Rules:**
- History Tracking (slider 1-5 weeks)
- Job Split Threshold (slider 3-15 points)

**Buttons:**
- "Save Settings"
- "Reset to Defaults"

### 5. View History Tab
- Shows last 5 generated rosters
- Each entry shows date and "View Details"/"Delete" buttons
- Details show the raw assignment data
- Empty state when no history

## Algorithm Requirements

### Job Assignment Logic
1. **Separate job types**: Independent jobs vs dependent jobs
2. **Assign independent jobs first** (sorted by points, highest first)
3. **Then assign dependent jobs** with smart pairing logic

### Scoring Algorithm (for each person per job)
```
Base Score = randomness * Math.random()
- (recentJobCount * recentPenalty)
- (pointDifference * workloadBalance)
+ bonuses for dependency logic
```

### Dependency Handling
- **High point combinations** (≥ splitThreshold): Prefer different people
- **Low point combinations** (< splitThreshold): Allow same person
- **Smart pairing**: Consider current workload when deciding

### Frequency Translation
- **Daily**: All 7 days
- **Every 2 days**: Mon, Wed, Fri, Sun
- **Every 3 days**: Mon, Thu
- **Weekly**: Random single day
- **Monthly**: Random weekend day

## Technical Requirements

### File Structure
- Single HTML file with embedded CSS and JavaScript
- Modern, responsive design
- Works offline completely
- No external dependencies except browser

### Browser Storage
- Use localStorage for all data persistence
- Keys: 'choreFamily', 'choreJobs', 'choreHistory', 'choreSettings'
- Auto-merge settings with defaults on load

### Mobile Responsiveness
- Responsive grid layouts
- Touch-friendly buttons and controls
- Readable on phone screens
- Print optimization for A4 landscape

### Print Functionality
- Clean, ink-efficient layout
- A4 landscape orientation
- Hide navigation and decorative elements
- Compact daily schedule format
- Week header with date

## Error Handling
- Validate all user inputs
- Prevent duplicate names
- Handle empty states gracefully
- Confirm destructive actions (delete)
- Graceful handling of missing dependencies

## Visual Design
- Modern, clean interface
- Color-coded job difficulties
- Smooth transitions and hover effects
- Consistent spacing and typography
- Blue primary color scheme (#007bff)
- Card-based layouts with subtle shadows

## Specific Features

### Pre-Job Dependencies
- When adding a job, option to require another job first
- Smart assignment algorithm considers dependencies
- Visual indication in job lists when dependencies exist
- Prevent circular dependencies

### Enhanced Balancing
- Consider historical assignments (configurable weeks back)
- Penalty system for recent job repetition
- Workload balancing with configurable strength
- Randomness control (deterministic to fully random)

### Settings Persistence
- All algorithm settings saved to localStorage
- Real-time slider value updates
- Reset to sensible defaults option
- Settings affect immediate next generation

## Example User Flow
1. Add family members: "Sarah", "John", "Alex"
2. Add jobs with exemptions: "Grocery Shopping" (exempt Alex - no license)
3. Set up dependency: "Mop Kitchen" requires "Vacuum Kitchen" first
4. Adjust settings: Increase workload balance, decrease randomness
5. Generate roster: Algorithm assigns fairly with dependencies respected
6. Print weekly schedule for family fridge
7. Next week: Generate again with history considered for fairness

## Success Criteria
- Intuitive interface that family members can use
- Fair distribution that feels right over multiple weeks
- Fast job creation and family management
- Dependencies work logically
- Print output is useful and readable
- Works reliably on phones and computers
- No external internet required

## What NOT to Include
- ❌ Age groups for family members
- ❌ Job categories (Kitchen, Bathroom, etc.)
- ❌ Complex "who can do this job" restrictions
- ❌ User accounts or cloud sync
- ❌ Any external dependencies
- ❌ Overly complex UI elements
- ❌ Sample/demo data by default